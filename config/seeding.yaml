# Seeding Pipeline Configuration
# Specific settings for VTT knowledge extraction

# VTT Processing
vtt:
  input_directory: "./transcripts"
  file_pattern: "*.vtt"
  recursive: true
  min_segment_duration: 2.0
  merge_short_segments: true

# Text Processing
processing:
  min_segment_tokens: 150
  max_segment_tokens: 800
  
# Knowledge Extraction  
extraction:
  confidence_threshold: 0.7
  entity_resolution_threshold: 0.85
  max_properties_per_node: 50
  max_entities_per_segment: 10
  min_insight_length: 20
  min_quote_length: 10

# Models
models:
  llm: "gemini-2.5-pro-preview-06-05"
  embeddings: "sentence-transformers/all-mpnet-base-v2"
  
# Neo4j Database
neo4j:
  # URI and credentials from environment variables:
  # NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD
  database: "neo4j"
  connection_timeout: 30
  max_connection_pool_size: 50

# Output and Checkpoints
paths:
  output_dir: "./output"
  checkpoint_dir: "./checkpoints"
  
# Rate Limiting
rate_limits:
  llm_requests_per_minute: 60
  llm_tokens_per_minute: 150000
  
# Feature Flags
features:
  enable_checkpoints: true
  enable_entity_resolution: true

# YouTube Search Configuration
youtube_search:
  enabled: true
  # API key from environment variable: YOUTUBE_API_KEY
  max_results: 5
  confidence_threshold: 0.7
  rate_limit_delay: 1.0  # seconds between searches

# Knowledge Discovery Configuration
knowledge_discovery:
  enabled: true
  gap_detection:
    min_gap_score: 0.5
    cooccurrence_threshold: 0.3
  missing_links:
    min_connection_score: 0.3
    max_suggestions: 10
  diversity_metrics:
    history_retention_days: 30
    trend_threshold: 0.05