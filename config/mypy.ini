[mypy]
# Common mypy configuration for type checking
python_version = 3.9
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
exclude = venv/

# Per-module options for common dependencies:
[mypy-feedparser.*]
ignore_missing_imports = True

[mypy-google.generativeai.*]
ignore_missing_imports = True

[mypy-neo4j.*]
ignore_missing_imports = True

[mypy-tenacity.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-setuptools.*]
ignore_missing_imports = True

[mypy-sentence_transformers.*]
ignore_missing_imports = True