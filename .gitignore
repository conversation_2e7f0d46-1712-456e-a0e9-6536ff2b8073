# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
*/venv/
*/test_env/
test_env/

# Environment variables
.env
.env.local
.env.test
.env.*

# Testing
.coverage
.coverage.*
coverage.xml
htmlcov/
.pytest_cache/
.tox/
.nox/
*.cover
.hypothesis/
test_results/
test_collection.txt
test_results_initial.txt
test_timings.txt
failed_tests.txt
test_inventory.txt
coverage_baseline.txt
pytest_output.txt
pytest_full_output.txt
test_collection_errors.txt
test_baseline.txt
test_failures.json
tests.log

# Logs
*.log
*.log.*
logs/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store

# IDE
.vscode/
.idea/
*.sublime-*

# Project specific
podcastknowledge/
import_errors.txt
import_errors_analysis.json
import_audit.txt
dependency_analysis.json

# Notebooks
.ipynb_checkpoints/

# Cache and state files
.cache/
*.cache
.state
.retry_state.json
.gemini_usage.json
.key_rotation_state.json
.youtube_cache.json

# Benchmarks and performance data
benchmarks/neo4j_benchmark_*.json
performance_*.json

# Backups
backups/

# Data files (should be stored externally)
data/transcripts/
*.vtt

# Component tracking files (generated)
seeding_pipeline/component_tracking/

# Temporary reports and documentation
youtube_url_analysis_report.md
ui/docs/
ui/backend/*.md
*_ANALYSIS.md
*-report.md
*-tracking.md

# InfraNodus (third-party tool)
infranodus/

# Debug and test files
debug*.js
test*.js
check*.js
monitor*.js
find*.js
puppeteer*.js
test_*.py
ui/backend/test_*.py
ui/frontend/test*.html
ui/frontend/src/test*.html
ui/frontend/**/test*.html

# Screenshots and images
*.png
dashboard-screenshot.png
podcast-screenshot.png

# Node.js
node_modules/
package-lock.json
package.json

# UI specific ignores
ui/frontend/node_modules/
ui/frontend/dist/
ui/frontend/.vite/
ui/backend/__pycache__/
ui/backend/*.pyc

# MCP config
.mcp.json

# Temporary markdown files
graph_issue_analysis.md
docs/infranodus-*.md
docs/search-filter-implementation-plan.md
seeding_pipeline/docs/semantic_gap_detection.md
ui/frontend/docs/session-*.md

# Data folder screenshots
data/*.png
"data/Screenshot*"
