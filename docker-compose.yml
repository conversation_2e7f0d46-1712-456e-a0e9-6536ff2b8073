version: '3.8'

services:
  # Neo4j Database
  neo4j:
    image: neo4j:5.14
    ports:
      - "7474:7474"  # Browser
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/changeme
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs

  # Transcriber Service
  transcriber:
    build:
      context: .
      target: transcriber
    environment:
      - GEMINI_API_KEY_1=${GEMINI_API_KEY_1}
      - GEMINI_API_KEY_2=${GEMINI_API_KEY_2}
      - LOG_LEVEL=INFO
    volumes:
      - ./data/transcripts:/app/data/transcripts
      - ./transcriber/logs:/app/logs
    depends_on:
      - neo4j

  # Seeding Pipeline Service
  seeding:
    build:
      context: .
      target: seeding
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=changeme
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./data/transcripts:/app/data/transcripts
      - ./checkpoints:/app/checkpoints
      - ./output:/app/output
    depends_on:
      - neo4j

volumes:
  neo4j_data:
  neo4j_logs: