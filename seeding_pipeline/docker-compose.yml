version: '3.8'

services:
  neo4j:
    image: neo4j:5.14-community
    container_name: vtt-neo4j
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
      NEO4J_server_memory_heap_initial__size: 512M
      NEO4J_server_memory_heap_max__size: 1G
      NEO4J_server_memory_pagecache_size: 256M
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "password", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1.5G
        reservations:
          memory: 512M

  vtt-pipeline:
    build:
      context: .
      dockerfile: Dockerfile.minimal
    container_name: vtt-pipeline
    environment:
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: password
      LOG_LEVEL: INFO
      PYTHONUNBUFFERED: 1
    volumes:
      - ./test_vtt:/app/input:ro
      - pipeline_output:/app/output
    depends_on:
      neo4j:
        condition: service_healthy
    command: ["python", "-m", "src.cli.cli", "--help"]
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  pipeline_output:
    driver: local

networks:
  default:
    driver: bridge