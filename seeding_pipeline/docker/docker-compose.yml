version: '3.8'

services:
  # Main application service
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    image: vtt-knowledge-pipeline:latest
    container_name: vtt-kg-app
    environment:
      # Neo4j connection
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
      # API keys (from .env file)
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      # Logging
      - PODCAST_KG_LOG_LEVEL=${LOG_LEVEL:-INFO}
      # Python
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=${ENVIRONMENT:-development}
    volumes:
      # Mount VTT data directory
      - ./vtt-files:/app/data/vtt-files
      - ./checkpoints:/app/checkpoints
      - ./logs:/app/logs
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - vtt-kg-network
    command: ["process-vtt", "--folder", "/app/data/vtt-files"]

  # API service (same image, different command)
  api:
    image: vtt-knowledge-pipeline:latest
    container_name: vtt-kg-api
    environment:
      # Neo4j connection
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
      # API Configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      # Logging
      - PODCAST_KG_LOG_LEVEL=${LOG_LEVEL:-INFO}
    ports:
      - "8000:8000"
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - vtt-kg-network
    command: ["python", "-m", "run_api"]

  # Neo4j database
  neo4j:
    image: neo4j:5.12.0
    container_name: vtt-kg-neo4j
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD:-password}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_default__listen__address=0.0.0.0
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    healthcheck:
      test: ["CMD", "neo4j", "status"]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 40s
    networks:
      - vtt-kg-network

networks:
  vtt-kg-network:
    driver: bridge

volumes:
  neo4j_data:
  neo4j_logs: