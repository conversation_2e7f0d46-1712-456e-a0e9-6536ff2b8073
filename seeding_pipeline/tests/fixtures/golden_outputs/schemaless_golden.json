{"extraction_mode": "schemaless", "created_at": "2025-06-09T23:46:49.564958", "segments_count": 5, "discovered_types": ["Podcast Host", "AI Expert", "AI Company", "Medical Institution", "AI Technology", "Performance Metric"], "results": [{"segment": {"text": "Welcome to the AI podcast. I'm your host <PERSON>, and today we're joined by Dr. <PERSON>, who is the Chief Technology Officer at TechCorp. <PERSON> has been working in artificial intelligence for over 15 years.", "start": 0.0, "end": 15.0, "speaker": "Host"}, "extraction": {"entities": [{"name": "<PERSON>", "type": "Podcast Host", "description": "Host of the AI-focused podcast"}, {"name": "Dr. <PERSON>", "type": "AI Expert", "description": "CTO with 15 years of AI experience"}, {"name": "TechCorp", "type": "AI Company", "description": "Company developing healthcare AI models"}, {"name": "Stanford Medical Center", "type": "Medical Institution", "description": "Healthcare partner for AI deployment"}, {"name": "Diagnostic AI System", "type": "AI Technology", "description": "System for analyzing medical imaging and detecting cancer"}, {"name": "95% Accuracy", "type": "Performance Metric", "description": "Accuracy rate of patient outcome predictions"}], "relationships": [{"source": "Dr. <PERSON>", "target": "TechCorp", "type": "LEADS_AI_AT"}, {"source": "TechCorp", "target": "Diagnostic AI System", "type": "DEVELOPED"}, {"source": "Diagnostic AI System", "target": "Stanford Medical Center", "type": "DEPLOYED_AT"}, {"source": "Dr. <PERSON>", "target": "AI Expert", "type": "HAS_EXPERTISE_IN", "properties": {"years": 15}}], "discovered_types": ["AI Company", "AI Technology", "AI Expert", "Performance Metric", "Podcast Host", "Medical Institution"]}}, {"segment": {"text": "Thanks for having me, <PERSON>. I'm excited to discuss how machine learning is transforming the healthcare industry. At TechCorp, we've developed several AI models that can predict patient outcomes with 95% accuracy.", "start": 15.0, "end": 30.0, "speaker": "Guest"}, "extraction": {"entities": [{"name": "<PERSON>", "type": "Podcast Host", "description": "Host of the AI-focused podcast"}, {"name": "Dr. <PERSON>", "type": "AI Expert", "description": "CTO with 15 years of AI experience"}, {"name": "TechCorp", "type": "AI Company", "description": "Company developing healthcare AI models"}, {"name": "Stanford Medical Center", "type": "Medical Institution", "description": "Healthcare partner for AI deployment"}, {"name": "Diagnostic AI System", "type": "AI Technology", "description": "System for analyzing medical imaging and detecting cancer"}, {"name": "95% Accuracy", "type": "Performance Metric", "description": "Accuracy rate of patient outcome predictions"}], "relationships": [{"source": "Dr. <PERSON>", "target": "TechCorp", "type": "LEADS_AI_AT"}, {"source": "TechCorp", "target": "Diagnostic AI System", "type": "DEVELOPED"}, {"source": "Diagnostic AI System", "target": "Stanford Medical Center", "type": "DEPLOYED_AT"}, {"source": "Dr. <PERSON>", "target": "AI Expert", "type": "HAS_EXPERTISE_IN", "properties": {"years": 15}}], "discovered_types": ["AI Company", "AI Technology", "AI Expert", "Performance Metric", "Podcast Host", "Medical Institution"]}}, {"segment": {"text": "That's fascinating. Can you tell us about some specific applications? I understand you've been working with Stanford Medical Center on a groundbreaking project.", "start": 30.0, "end": 40.0, "speaker": "Host"}, "extraction": {"entities": [{"name": "<PERSON>", "type": "Podcast Host", "description": "Host of the AI-focused podcast"}, {"name": "Dr. <PERSON>", "type": "AI Expert", "description": "CTO with 15 years of AI experience"}, {"name": "TechCorp", "type": "AI Company", "description": "Company developing healthcare AI models"}, {"name": "Stanford Medical Center", "type": "Medical Institution", "description": "Healthcare partner for AI deployment"}, {"name": "Diagnostic AI System", "type": "AI Technology", "description": "System for analyzing medical imaging and detecting cancer"}, {"name": "95% Accuracy", "type": "Performance Metric", "description": "Accuracy rate of patient outcome predictions"}], "relationships": [{"source": "Dr. <PERSON>", "target": "TechCorp", "type": "LEADS_AI_AT"}, {"source": "TechCorp", "target": "Diagnostic AI System", "type": "DEVELOPED"}, {"source": "Diagnostic AI System", "target": "Stanford Medical Center", "type": "DEPLOYED_AT"}, {"source": "Dr. <PERSON>", "target": "AI Expert", "type": "HAS_EXPERTISE_IN", "properties": {"years": 15}}], "discovered_types": ["AI Company", "AI Technology", "AI Expert", "Performance Metric", "Podcast Host", "Medical Institution"]}}, {"segment": {"text": "Absolutely. We partnered with Stanford Medical Center last year to deploy our diagnostic AI system. The system analyzes medical imaging data and can detect early signs of cancer that human doctors might miss. It's been incredibly successful.", "start": 40.0, "end": 55.0, "speaker": "Guest"}, "extraction": {"entities": [{"name": "<PERSON>", "type": "Podcast Host", "description": "Host of the AI-focused podcast"}, {"name": "Dr. <PERSON>", "type": "AI Expert", "description": "CTO with 15 years of AI experience"}, {"name": "TechCorp", "type": "AI Company", "description": "Company developing healthcare AI models"}, {"name": "Stanford Medical Center", "type": "Medical Institution", "description": "Healthcare partner for AI deployment"}, {"name": "Diagnostic AI System", "type": "AI Technology", "description": "System for analyzing medical imaging and detecting cancer"}, {"name": "95% Accuracy", "type": "Performance Metric", "description": "Accuracy rate of patient outcome predictions"}], "relationships": [{"source": "Dr. <PERSON>", "target": "TechCorp", "type": "LEADS_AI_AT"}, {"source": "TechCorp", "target": "Diagnostic AI System", "type": "DEVELOPED"}, {"source": "Diagnostic AI System", "target": "Stanford Medical Center", "type": "DEPLOYED_AT"}, {"source": "Dr. <PERSON>", "target": "AI Expert", "type": "HAS_EXPERTISE_IN", "properties": {"years": 15}}], "discovered_types": ["AI Company", "AI Technology", "AI Expert", "Performance Metric", "Podcast Host", "Medical Institution"]}}, {"segment": {"text": "What about the ethical considerations? How do you ensure patient privacy while training these models?", "start": 55.0, "end": 65.0, "speaker": "Host"}, "extraction": {"entities": [{"name": "<PERSON>", "type": "Podcast Host", "description": "Host of the AI-focused podcast"}, {"name": "Dr. <PERSON>", "type": "AI Expert", "description": "CTO with 15 years of AI experience"}, {"name": "TechCorp", "type": "AI Company", "description": "Company developing healthcare AI models"}, {"name": "Stanford Medical Center", "type": "Medical Institution", "description": "Healthcare partner for AI deployment"}, {"name": "Diagnostic AI System", "type": "AI Technology", "description": "System for analyzing medical imaging and detecting cancer"}, {"name": "95% Accuracy", "type": "Performance Metric", "description": "Accuracy rate of patient outcome predictions"}], "relationships": [{"source": "Dr. <PERSON>", "target": "TechCorp", "type": "LEADS_AI_AT"}, {"source": "TechCorp", "target": "Diagnostic AI System", "type": "DEVELOPED"}, {"source": "Diagnostic AI System", "target": "Stanford Medical Center", "type": "DEPLOYED_AT"}, {"source": "Dr. <PERSON>", "target": "AI Expert", "type": "HAS_EXPERTISE_IN", "properties": {"years": 15}}], "discovered_types": ["AI Company", "AI Technology", "AI Expert", "Performance Metric", "Podcast Host", "Medical Institution"]}}]}