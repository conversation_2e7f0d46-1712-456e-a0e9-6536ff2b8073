# Dockerfile for integration testing
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements
COPY requirements.txt requirements-dev.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r requirements-dev.txt

# Copy application code
COPY . .

# Create directories for test results
RUN mkdir -p /app/test-results /app/test-audio

# Set Python path
ENV PYTHONPATH=/app

# Default command runs integration tests
CMD ["pytest", "tests/integration", "-v", "--tb=short"]