version: '3.8'

services:
  neo4j-test:
    image: neo4j:5.14
    container_name: podcast-kg-neo4j-test
    environment:
      - NEO4J_AUTH=neo4j/testpassword
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_memory_pagecache_size=512M
      - NEO4J_dbms_memory_heap_max__size=512M
    ports:
      - "7688:7687"  # Different port to avoid conflicts
      - "7475:7474"  # Browser interface
    volumes:
      - neo4j-test-data:/data
      - neo4j-test-logs:/logs
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "testpassword", "RETURN 1"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  neo4j-test-data:
  neo4j-test-logs: