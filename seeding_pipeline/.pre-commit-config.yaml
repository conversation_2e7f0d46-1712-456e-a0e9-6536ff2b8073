repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-json
      - id: check-toml
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: ["--max-line-length=88", "--extend-ignore=E203,W503"]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-tqdm]
        args: ["--ignore-missing-imports"]

  # Run quick unit tests
  - repo: local
    hooks:
      - id: quick-tests
        name: Quick Unit Tests
        entry: bash -c 'pytest tests/unit/test_models.py tests/unit/test_config.py -v --tb=short || true'
        language: system
        pass_filenames: false
        stages: [commit]
        
      - id: coverage-check
        name: Coverage Check
        entry: bash -c 'coverage report --fail-under=8.43 2>/dev/null || echo "Coverage check skipped - run full test suite to update"'
        language: system
        pass_filenames: false
        stages: [push]

# To skip hooks in emergency: git commit --no-verify
# To run all hooks manually: pre-commit run --all-files
# To run specific hook: pre-commit run <hook-id> --all-files