# Semantic Clustering Configuration
# 
# This file contains configuration parameters for the HDBSCAN clustering system.
# Adjust these parameters to tune clustering behavior.
#
# IMPORTANT: HDBSCAN is NOT self-healing. If results are poor, manually adjust
# these parameters and re-run clustering.

clustering:
  # Minimum cluster size calculation method
  # Options: 'sqrt' (dynamic based on data size) or 'fixed' (use fixed value)
  min_cluster_size_formula: 'fixed'
  
  # If min_cluster_size_formula is 'fixed', use this value
  # Typical range: 5-50 depending on dataset size
  min_cluster_size_fixed: 5
  
  # Minimum number of samples in neighborhood for point to be core point
  # Lower values = more granular clusters
  # Typical range: 2-10
  min_samples: 1
  
  # Cluster selection epsilon for HDBSCAN
  # Controls how close points must be to merge into same cluster
  # Lower values = tighter, more numerous clusters
  # Higher values = looser, fewer clusters
  # Typical range: 0.1-0.5 for cosine distance on embeddings
  epsilon: 0.5

# Quality Warning Thresholds
quality:
  # Maximum allowed ratio of outliers before warning
  # Range: 0.0-1.0 (0.3 = 30%)
  max_outlier_ratio: 0.3
  
  # Minimum number of clusters expected
  min_clusters: 3
  
  # Maximum size for a single cluster before warning
  max_cluster_size: 100

# Embedding Configuration
embeddings:
  # Expected dimension of embeddings from Gemini
  expected_dimensions: 768

# Label Generation Configuration
label_generation:
  # Temperature for LLM label generation (controls creativity)
  # Range: 0.0-1.0 (0.3 = somewhat creative)
  temperature: 0.3
  
  # Maximum number of words in generated labels
  max_label_words: 3

# Pipeline Integration Configuration
pipeline:
  # Whether to automatically trigger clustering after episode processing
  # Set to false to disable automatic clustering
  auto_clustering_enabled: true
  
  # Minimum number of successfully processed episodes to trigger clustering
  # Only applies when auto_clustering_enabled is true
  min_episodes_for_clustering: 1

