aiohappyeyeballs==2.6.1
aiohttp==3.12.6
aiosignal==1.3.2
alabaster==0.7.16
annotated-types==0.7.0
anyio==3.7.1
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.5
attrs==25.3.0
babel==2.17.0
bandit==1.7.5
beautifulsoup4==4.13.4
black==23.11.0
bleach==6.2.0
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.2
click==8.2.1
comm==0.2.2
coverage==7.8.2
dataclasses-json==0.6.7
debugpy==1.8.14
decorator==5.2.1
defusedxml==0.7.1
deprecation==2.1.0
distlib==0.3.9
distro==1.9.0
docker==7.1.0
docutils==0.20.1
execnet==2.1.1
executing==2.2.0
fastapi==0.104.1
fastjsonschema==2.21.1
filelock==3.18.0
flake8==6.1.0
fqdn==1.5.1
frozenlist==1.6.0
fsspec==2025.5.1
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.4.0
google-api-core==2.25.0rc1
google-auth==2.40.2
google-generativeai==0.3.2
googleapis-common-protos==1.70.0
graphviz==0.20.1
greenlet==3.2.2
grpcio==1.71.0
grpcio-status==1.62.3
h11==0.16.0
hf-xet==1.1.2
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.32.3
identify==2.6.12
idna==3.10
imagesize==1.4.1
iniconfig==2.1.0
ipykernel==6.29.5
ipython==8.17.2
ipywidgets==8.1.7
isoduration==20.11.0
isort==5.12.0
jedi==0.19.2
Jinja2==3.1.6
joblib==1.5.1
json5==0.12.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter==1.0.0
jupyter_client==8.6.3
jupyter-console==6.6.3
jupyter_core==5.8.1
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_server==2.16.0
jupyter_server_terminals==0.5.3
jupyterlab==4.4.3
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.15
langchain==0.1.0
langchain-community==0.0.20
langchain-core==0.1.23
langchain-google-genai==0.0.5
langsmith==0.0.87
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mccabe==0.7.0
mdurl==0.1.2
mistune==3.1.3
mpmath==1.3.0
multidict==6.4.4
mypy==1.7.1
mypy_extensions==1.1.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
neo4j==5.14.0
nest-asyncio==1.6.0
networkx==3.1
nltk==3.9.1
nodeenv==1.9.1
notebook==7.4.3
notebook_shim==0.2.4
numpy==1.24.3
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
openai==1.6.1
overrides==7.7.0
packaging==23.2
pandocfilters==1.5.1
parso==0.8.4
pathspec==0.12.1
pbr==6.1.1
pexpect==4.9.0
pillow==11.2.1
pip==25.1.1
platformdirs==4.3.8
pluggy==1.6.0
pre-commit==3.5.0
prometheus_client==0.22.0
prompt_toolkit==3.0.51
propcache==0.3.1
proto-plus==1.26.1
protobuf==4.25.8
psutil==5.9.6
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.11.1
pycparser==2.22
pydantic==2.5.0
pydantic_core==2.14.1
pyflakes==3.1.0
Pygments==2.19.1
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-timeout==2.2.0
pytest-xdist==3.5.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
python-json-logger==3.3.0
pytz==2025.2
PyYAML==6.0.1
pyzmq==26.4.0
qtconsole==5.6.1
QtPy==2.4.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.11.4
Send2Trash==1.8.3
sentence-transformers==2.2.2
sentencepiece==0.2.0
setuptools==66.1.1
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
snowballstemmer==3.0.1
soupsieve==2.7
Sphinx==7.2.6
sphinx-rtd-theme==2.0.0
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.27.0
stevedore==5.4.1
sympy==1.14.0
tenacity==8.5.0
terminado==0.18.1
testcontainers==3.7.1
threadpoolctl==3.6.0
tinycss2==1.4.0
tokenizers==0.21.1
torch==2.7.0
torchvision==0.22.0
tornado==6.5.1
tqdm==4.66.1
traitlets==5.14.3
transformers==4.52.4
triton==3.3.0
types-python-dateutil==2.9.0.20250516
types-requests==********
types-tqdm==********
types-urllib3==**********
typing_extensions==4.13.2
typing-inspect==0.9.0
uri-template==1.3.0
urllib3==2.4.0
uvicorn==0.24.0
uvloop==0.21.0
virtualenv==20.31.2
watchfiles==1.0.5
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
widgetsnbextension==4.0.14
wrapt==1.17.2
yarl==1.20.0
