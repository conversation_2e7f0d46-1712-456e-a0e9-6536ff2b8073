# Minimal Requirements for VTT Knowledge Graph Pipeline
# This file contains only the essential dependencies needed to run the core pipeline

# Core Dependencies (required)
neo4j==5.14.0          # Graph database client
python-dotenv==1.0.0   # Environment variable management
pydantic==2.5.0        # Data validation and models
PyYAML==6.0.1          # Configuration file parsing

# Optional Dependencies (enhanced functionality)
# Uncomment these as needed:

# For progress bars:
# tqdm==4.66.1

# For enhanced embeddings (otherwise uses mocks):
# google-generativeai==0.3.2
# numpy==1.24.3

# For resource monitoring (otherwise uses mocks):
# psutil==5.9.6

# For YouTube search functionality:
# google-api-python-client==2.108.0

# For API server:
# fastapi==0.104.1
# uvicorn[standard]==0.24.0

# For advanced LLM features:
# openai==1.6.1
# google-genai>=1.0.0