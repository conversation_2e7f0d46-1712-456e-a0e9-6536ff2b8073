#!/bin/bash
# Docker and Neo4j Installation Guide

echo "Docker Installation for Ubuntu/Debian"
echo "===================================="
echo ""
echo "Step 1: Install Docker"
echo "---------------------"
echo "Run these commands to install Docker:"
echo ""
echo "# Update package index"
echo "sudo apt-get update"
echo ""
echo "# Install prerequisites"
echo "sudo apt-get install -y \\"
echo "    ca-certificates \\"
echo "    curl \\"
echo "    gnupg \\"
echo "    lsb-release"
echo ""
echo "# Add Docker's official GPG key"
echo "sudo mkdir -m 0755 -p /etc/apt/keyrings"
echo "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg"
echo ""
echo "# Set up the repository"
echo 'echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null'
echo ""
echo "# Install Docker Engine"
echo "sudo apt-get update"
echo "sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin"
echo ""
echo "# Add your user to docker group (to run without sudo)"
echo "sudo usermod -aG docker $USER"
echo ""
echo "# Apply group changes (or logout/login)"
echo "newgrp docker"
echo ""
echo "Step 2: Verify Docker Installation"
echo "----------------------------------"
echo "docker --version"
echo "docker run hello-world"
echo ""
echo "Step 3: Run Neo4j with Docker"
echo "-----------------------------"
echo "Once Docker is installed, run this command:"
echo ""
cat << 'EOF'
docker run -d \
  --name neo4j \
  --restart unless-stopped \
  -p 7474:7474 \
  -p 7687:7687 \
  -v $HOME/neo4j/data:/data \
  -v $HOME/neo4j/logs:/logs \
  -v $HOME/neo4j/import:/var/lib/neo4j/import \
  -v $HOME/neo4j/plugins:/plugins \
  -e NEO4J_AUTH=neo4j/password \
  neo4j:5.15.0-community
EOF
echo ""
echo "Step 4: Verify Neo4j is Running"
echo "-------------------------------"
echo "# Check container status"
echo "docker ps"
echo ""
echo "# View Neo4j logs"
echo "docker logs neo4j"
echo ""
echo "# Test connection"
echo "cd ~/podcastknowledge/seeding_pipeline"
echo "source venv/bin/activate"
echo "python test_neo4j_connection.py"
echo ""
echo "Step 5: Access Neo4j"
echo "-------------------"
echo "Browser UI: http://localhost:7474"
echo "Bolt URL: bolt://localhost:7687"
echo "Username: neo4j"
echo "Password: password"
echo ""
echo "Useful Docker Commands"
echo "---------------------"
echo "# Stop Neo4j"
echo "docker stop neo4j"
echo ""
echo "# Start Neo4j"
echo "docker start neo4j"
echo ""
echo "# Remove Neo4j container (data persists in volumes)"
echo "docker rm -f neo4j"
echo ""
echo "# View logs"
echo "docker logs -f neo4j"
echo ""