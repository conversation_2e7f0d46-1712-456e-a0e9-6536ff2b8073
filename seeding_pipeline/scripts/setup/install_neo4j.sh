#!/bin/bash
# Neo4j Community Edition Installation Script

echo "Neo4j Community Edition Installation Options"
echo "==========================================="
echo ""
echo "Option 1: Download and Run Neo4j Community Edition (Recommended)"
echo "------------------------------------------------"
echo "1. Download Neo4j Community Edition:"
echo "   wget https://neo4j.com/artifact.php?name=neo4j-community-5.15.0-unix.tar.gz -O neo4j-community-5.15.0-unix.tar.gz"
echo ""
echo "2. Extract the archive:"
echo "   tar -xzf neo4j-community-5.15.0-unix.tar.gz"
echo ""
echo "3. Move to a permanent location:"
echo "   sudo mv neo4j-community-5.15.0 /opt/neo4j"
echo ""
echo "4. Start Neo4j:"
echo "   /opt/neo4j/bin/neo4j start"
echo ""
echo "5. Access Neo4j Browser at: http://localhost:7474"
echo "   Default credentials: neo4j/neo4j (you'll be prompted to change)"
echo ""
echo "Option 2: Install via apt (Ubuntu/Debian)"
echo "----------------------------------------"
echo "1. Add Neo4j repository:"
echo "   wget -O - https://debian.neo4j.com/neotechnology.gpg.key | sudo apt-key add -"
echo "   echo 'deb https://debian.neo4j.com stable latest' | sudo tee /etc/apt/sources.list.d/neo4j.list"
echo ""
echo "2. Update and install:"
echo "   sudo apt-get update"
echo "   sudo apt-get install neo4j"
echo ""
echo "3. Start Neo4j service:"
echo "   sudo systemctl start neo4j"
echo ""
echo "Option 3: Run with Docker (if Docker is installed)"
echo "-------------------------------------------------"
echo "docker run -d --name neo4j \\"
echo "  -p 7474:7474 -p 7687:7687 \\"
echo "  -e NEO4J_AUTH=neo4j/password \\"
echo "  neo4j:5.15.0-community"
echo ""
echo "Which option would you like to use?"