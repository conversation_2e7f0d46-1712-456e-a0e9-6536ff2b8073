[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vtt-knowledge-extractor"
version = "0.1.0"
description = "VTT transcript knowledge extraction and graph seeding pipeline"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "VTT KG Team", email = "<EMAIL>"},
]
dependencies = [
    "neo4j>=5.14.0",
    "neo4j-graphrag>=1.7.0",
    "python-dotenv>=1.0.0",
    "numpy>=1.24.3",
    "google-generativeai>=0.3.2",
    "google-genai>=1.0.0",
    "psutil>=5.9.6",
    "pyyaml>=6.0.1",
    "pydantic>=2.5.0",
    "google-api-python-client>=2.108.0",
]

[project.optional-dependencies]
scientific = [
    "scipy>=1.11.4",
    "networkx>=3.1",
]
api = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
]
embeddings-local = [
    "torch>=2.1.0",
    "sentence-transformers>=2.2.2",
]
dev = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "mypy>=1.7.1",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "pre-commit>=3.5.0",
]

[project.scripts]
vtt-extract = "src.cli.cli:main"
vtt-knowledge = "src.cli.cli:main"

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--cov=src",
    "--cov-branch",
    "--cov-report=term-missing:skip-covered",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
norecursedirs = ["tests/archived_obsolete"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
    "performance: marks tests as performance tests",
    "benchmark: marks tests as benchmark tests",
    "external: marks tests that require external services",
    "memory_intensive: marks tests that use significant memory",
    "vtt: marks tests as VTT-specific",
    "vtt_parser: marks tests for VTT parser functionality",
    "vtt_segmentation: marks tests for VTT segmentation",
    "vtt_extraction: marks tests for VTT knowledge extraction",
    "critical: marks tests as critical path",
    "neo4j: marks tests that require Neo4j",
]
timeout = 30

[tool.coverage.run]
branch = true
source = ["src"]
omit = [
    "*/tests/*",
    "*/__init__.py",
    "*/migrations/*",
]

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = true
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "@abstractmethod",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 100

[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''