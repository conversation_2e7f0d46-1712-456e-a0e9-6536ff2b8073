# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
htmlcov/
*.cover
*.log
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/

# Virtual environments
bin/
include/
lib/
lib64/
share/
pyvenv.cfg

# IDEs
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Project specific
data/
logs/
cache/
checkpoints/
test_checkpoints/
base/
audio/
test_audio/
output/
test_output/
processed_podcasts/
profiling_results/
component_tracking/
test_tracking/
*.db
*.sqlite
*.egg-info/
dist/
build/
benchmarks/
htmlcov/

# Git
.git/
.gitignore
.gitattributes

# Documentation build
docs/_build/
docs/.doctrees/

# Testing
.coverage
htmlcov/
.tox/
.pytest_cache/
tests/__pycache__/

# Environment files (keep .env.example)
.env
.env.local
.env.*.local

# Temporary files
*.tmp
*.temp
*.bak
*.orig

# OS files
.DS_Store
Thumbs.db

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Development files
*.md
!README.md
!CHANGELOG.md
Makefile
notebooks/