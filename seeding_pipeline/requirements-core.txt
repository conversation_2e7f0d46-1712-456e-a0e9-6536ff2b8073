# Core Dependencies - Minimal set for basic VTT processing
# Installs in ~60 seconds on modest hardware

# Database
neo4j==5.14.0

# Configuration
python-dotenv==1.0.0

# LLM Integration (Google Gemini only)
google-generativeai==0.3.2

# System Monitoring
psutil==5.9.6

# Graph Analysis (required by ImportanceScorer)
networkx==3.1

# Numerical Operations (required for embeddings)
numpy==1.24.4
scipy==1.10.1

# Progress Display
tqdm==4.66.1

# Configuration Files
PyYAML==6.0.1