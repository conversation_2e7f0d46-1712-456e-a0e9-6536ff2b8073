FROM python:3.11-slim

# Create non-root user for security
RUN groupadd -g 999 vttpipeline && \
    useradd -r -u 999 -g vttpipeline vttpipeline

# Set working directory
WORKDIR /app

# Install system dependencies if needed
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements-core.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-core.txt

# Copy application source code
COPY src/ ./src/
COPY scripts/ ./scripts/

# Set proper ownership
RUN chown -R vttpipeline:vttpipeline /app

# Switch to non-root user
USER vttpipeline

# Default command
CMD ["python", "-m", "src.cli.cli"]