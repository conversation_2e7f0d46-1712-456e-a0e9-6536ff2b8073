{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Component Tracking Dashboard\n", "\n", "This notebook provides examples of how to analyze component impact and make data-driven decisions about which components to keep or remove."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup imports\n", "import sys\n", "import os\n", "sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath('.'))))\n", "\n", "from src.utils.component_tracker import (\n", "    analyze_component_impact,\n", "    compare_component_versions,\n", "    identify_redundancies,\n", "    ComponentTracker\n", ")\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Set up plotting style\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Component Impact Analysis\n", "\n", "Analyze the impact of individual components on the extraction pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List of components to analyze\n", "components = [\n", "    \"segment_preprocessor\",\n", "    \"entity_resolution\", \n", "    \"metadata_enricher\",\n", "    \"quote_extractor\"\n", "]\n", "\n", "# Analyze each component\n", "impact_reports = {}\n", "for component in components:\n", "    try:\n", "        impact = analyze_component_impact(component)\n", "        impact_reports[component] = impact\n", "        print(f\"\\n=== {component} ===\")\n", "        print(f\"Recommendation: {impact.get('recommendation', 'No data')}\")\n", "        \n", "        if 'metrics_summary' in impact:\n", "            metrics = impact['metrics_summary']\n", "            print(f\"Total executions: {metrics.get('total_executions', 0)}\")\n", "            print(f\"Success rate: {metrics.get('success_rate', 0):.2%}\")\n", "            print(f\"Avg execution time: {metrics.get('avg_execution_time', 0):.3f}s\")\n", "    except Exception as e:\n", "        print(f\"Error analyzing {component}: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Performance Visualization\n", "\n", "Visualize component performance metrics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract performance data\n", "perf_data = []\n", "for component, impact in impact_reports.items():\n", "    if 'metrics_summary' in impact:\n", "        metrics = impact['metrics_summary']\n", "        perf_data.append({\n", "            'Component': component,\n", "            'Execution Time (s)': metrics.get('avg_execution_time', 0),\n", "            'Success Rate': metrics.get('success_rate', 0),\n", "            'Executions': metrics.get('total_executions', 0)\n", "        })\n", "\n", "if perf_data:\n", "    df = pd.DataFrame(perf_data)\n", "    \n", "    # Create subplots\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "    \n", "    # Execution time comparison\n", "    df.plot(x='Component', y='Execution Time (s)', kind='bar', ax=axes[0], legend=False)\n", "    axes[0].set_title('Average Execution Time')\n", "    axes[0].set_ylabel('Time (seconds)')\n", "    axes[0].tick_params(axis='x', rotation=45)\n", "    \n", "    # Success rate comparison\n", "    df.plot(x='Component', y='Success Rate', kind='bar', ax=axes[1], legend=False, color='green')\n", "    axes[1].set_title('Success Rate')\n", "    axes[1].set_ylabel('Success Rate')\n", "    axes[1].set_ylim(0, 1.1)\n", "    axes[1].tick_params(axis='x', rotation=45)\n", "    \n", "    # Execution count\n", "    df.plot(x='Component', y='Executions', kind='bar', ax=axes[2], legend=False, color='orange')\n", "    axes[2].set_title('Total Executions')\n", "    axes[2].set_ylabel('Count')\n", "    axes[2].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No performance data available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Component Contributions\n", "\n", "Analyze what each component contributes to the extraction pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze contributions\n", "contribution_data = []\n", "for component, impact in impact_reports.items():\n", "    if 'contributions' in impact:\n", "        for contrib in impact['contributions']:\n", "            contribution_data.append({\n", "                'Component': component,\n", "                'Type': contrib['type'],\n", "                'Count': contrib['total_count'],\n", "                'Occurrences': contrib['occurrences']\n", "            })\n", "\n", "if contribution_data:\n", "    contrib_df = pd.DataFrame(contribution_data)\n", "    \n", "    # Pivot for visualization\n", "    pivot_df = contrib_df.pivot_table(\n", "        index='Component', \n", "        columns='Type', \n", "        values='Count', \n", "        fill_value=0\n", "    )\n", "    \n", "    # Create stacked bar chart\n", "    ax = pivot_df.plot(kind='bar', stacked=True, figsize=(10, 6))\n", "    ax.set_title('Component Contributions by Type')\n", "    ax.set_ylabel('Total Contributions')\n", "    ax.tick_params(axis='x', rotation=45)\n", "    plt.legend(title='Contribution Type', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No contribution data available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Version Comparison\n", "\n", "Compare different versions of components to track improvements or regressions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Compare two versions of a component\n", "component_name = \"segment_preprocessor\"\n", "v1 = \"1.0.0\"\n", "v2 = \"1.1.0\"\n", "\n", "try:\n", "    comparison = compare_component_versions(component_name, v1, v2)\n", "    \n", "    print(f\"\\nVersion Comparison: {component_name}\")\n", "    print(f\"Recommendation: {comparison.get('recommendation', 'No data')}\")\n", "    \n", "    if 'version_comparison' in comparison:\n", "        for version, metrics in comparison['version_comparison'].items():\n", "            print(f\"\\nVersion {version}:\")\n", "            print(f\"  Executions: {metrics.get('total_executions', 0)}\")\n", "            print(f\"  Avg time: {metrics.get('avg_execution_time', 0):.3f}s\")\n", "            print(f\"  Success rate: {metrics.get('success_rate', 0):.2%}\")\n", "except Exception as e:\n", "    print(f\"Error comparing versions: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Redundancy Analysis\n", "\n", "Identify components with overlapping functionality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find redundant components\n", "redundancies = identify_redundancies()\n", "\n", "if redundancies:\n", "    print(\"\\nPotential Redundancies Found:\")\n", "    print(\"=\" * 60)\n", "    \n", "    for r in redundancies[:5]:  # Show top 5\n", "        print(f\"\\n{r['component1']} <-> {r['component2']}\")\n", "        print(f\"  Shared contribution: {r['shared_contribution']}\")\n", "        print(f\"  Overlap ratio: {r['overlap_ratio']:.2%}\")\n", "        \n", "    # Visualize redundancy network\n", "    if len(redundancies) > 0:\n", "        # Create adjacency matrix for visualization\n", "        components = set()\n", "        for r in redundancies:\n", "            components.add(r['component1'])\n", "            components.add(r['component2'])\n", "        \n", "        components = sorted(list(components))\n", "        n = len(components)\n", "        \n", "        if n > 1:\n", "            import numpy as np\n", "            \n", "            matrix = np.zeros((n, n))\n", "            for r in redundancies:\n", "                i = components.index(r['component1'])\n", "                j = components.index(r['component2'])\n", "                matrix[i, j] = r['overlap_ratio']\n", "                matrix[j, i] = r['overlap_ratio']\n", "            \n", "            # Plot heatmap\n", "            plt.figure(figsize=(8, 6))\n", "            sns.heatmap(matrix, \n", "                       xticklabels=components, \n", "                       yticklabels=components,\n", "                       annot=True, \n", "                       fmt='.2f',\n", "                       cmap='YlOrRd',\n", "                       vmin=0, vmax=1)\n", "            plt.title('Component Redundancy Matrix')\n", "            plt.tight_layout()\n", "            plt.show()\n", "else:\n", "    print(\"No redundancies found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Decision Matrix\n", "\n", "Create a decision matrix to help determine which components to keep or remove."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Build decision matrix\n", "decision_data = []\n", "\n", "for component, impact in impact_reports.items():\n", "    if 'metrics_summary' in impact:\n", "        metrics = impact['metrics_summary']\n", "        \n", "        # Calculate scores\n", "        performance_score = 1.0 - min(metrics.get('avg_execution_time', 0) / 5.0, 1.0)\n", "        reliability_score = metrics.get('success_rate', 0)\n", "        \n", "        # Count contributions\n", "        contribution_score = 0\n", "        if 'contributions' in impact:\n", "            total_contributions = sum(c['total_count'] for c in impact['contributions'])\n", "            contribution_score = min(total_contributions / 100, 1.0)\n", "        \n", "        # Overall score\n", "        overall_score = (performance_score + reliability_score + contribution_score) / 3\n", "        \n", "        decision_data.append({\n", "            'Component': component,\n", "            'Performance': performance_score,\n", "            'Reliability': reliability_score,\n", "            'Contribution': contribution_score,\n", "            'Overall': overall_score,\n", "            'Recommendation': impact.get('recommendation', 'No data')\n", "        })\n", "\n", "if decision_data:\n", "    decision_df = pd.DataFrame(decision_data)\n", "    decision_df = decision_df.sort_values('Overall', ascending=False)\n", "    \n", "    # Display decision matrix\n", "    print(\"\\nComponent Decision Matrix\")\n", "    print(\"=\" * 80)\n", "    print(decision_df.to_string(index=False))\n", "    \n", "    # Visualize scores\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    \n", "    x = np.arange(len(decision_df))\n", "    width = 0.2\n", "    \n", "    ax.bar(x - width, decision_df['Performance'], width, label='Performance')\n", "    ax.bar(x, decision_df['Reliability'], width, label='Reliability')\n", "    ax.bar(x + width, decision_df['Contribution'], width, label='Contribution')\n", "    \n", "    ax.set_xlabel('Component')\n", "    ax.set_ylabel('Score (0-1)')\n", "    ax.set_title('Component Scores by Category')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(decision_df['Component'], rotation=45)\n", "    ax.legend()\n", "    ax.set_ylim(0, 1.1)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No decision data available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Export Report\n", "\n", "Export the analysis results for documentation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate summary report\n", "report = {\n", "    \"analysis_date\": pd.Timestamp.now().isoformat(),\n", "    \"components_analyzed\": len(impact_reports),\n", "    \"recommendations\": {},\n", "    \"redundancies_found\": len(redundancies),\n", "    \"decision_summary\": []\n", "}\n", "\n", "# Add recommendations\n", "for component, impact in impact_reports.items():\n", "    report[\"recommendations\"][component] = impact.get('recommendation', 'No data')\n", "\n", "# Add decision summary\n", "if decision_data:\n", "    for item in decision_data:\n", "        if item['Overall'] < 0.3:\n", "            action = \"REMOVE\"\n", "        elif item['Overall'] < 0.6:\n", "            action = \"OPTIMIZE\"\n", "        else:\n", "            action = \"KEEP\"\n", "        \n", "        report[\"decision_summary\"].append({\n", "            \"component\": item['Component'],\n", "            \"score\": round(item['Overall'], 2),\n", "            \"action\": action\n", "        })\n", "\n", "# Save report\n", "import json\n", "with open('component_analysis_report.json', 'w') as f:\n", "    json.dump(report, f, indent=2)\n", "\n", "print(\"\\nAnalysis Summary:\")\n", "print(f\"Components analyzed: {report['components_analyzed']}\")\n", "print(f\"Redundancies found: {report['redundancies_found']}\")\n", "print(\"\\nRecommended Actions:\")\n", "for item in report['decision_summary']:\n", "    print(f\"  {item['component']}: {item['action']} (score: {item['score']})\")\n", "print(\"\\nFull report saved to: component_analysis_report.json\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}