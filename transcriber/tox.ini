[tox]
envlist = py{38,39,310,311}, lint, coverage
isolated_build = True
skip_missing_interpreters = True

[testenv]
deps =
    pytest>=8.0
    pytest-asyncio>=1.0
    pytest-mock>=3.0
    pytest-timeout>=2.0
    pytest-cov>=4.0
setenv =
    PYTHONPATH = {toxinidir}
    # Load test environment variables
    GEMINI_API_KEY_1 = test_key_1
    GEMINI_API_KEY_2 = test_key_2
    PODCAST_TEST_MODE = true
commands =
    pytest {posargs}

[testenv:coverage]
deps =
    {[testenv]deps}
    coverage[toml]>=7.0
commands =
    coverage run -m pytest
    coverage report
    coverage html

[testenv:lint]
deps =
    flake8>=6.0
    black>=23.0
    isort>=5.0
    mypy>=1.0
commands =
    flake8 src tests
    black --check src tests
    isort --check-only src tests
    mypy src --ignore-missing-imports

[testenv:format]
deps =
    black>=23.0
    isort>=5.0
commands =
    black src tests
    isort src tests

[testenv:docs]
deps =
    sphinx>=7.0
    sphinx-rtd-theme>=2.0
commands =
    sphinx-build -b html docs docs/_build/html

# Test environment for minimal dependencies
[testenv:minimal]
deps =
    pytest>=8.0
    pytest-asyncio>=1.0
commands =
    pytest tests/test_config.py tests/test_progress_tracker.py -v

# Test environment for integration tests only
[testenv:integration]
deps =
    {[testenv]deps}
passenv = 
    GEMINI_*
commands =
    pytest -m integration {posargs}

[flake8]
max-line-length = 100
extend-ignore = E203, W503
exclude = 
    .git,
    __pycache__,
    venv,
    .venv,
    .tox,
    build,
    dist

[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    e2e: marks tests as end-to-end tests
    network: marks tests that require network access
    slow: marks tests as slow running
    performance: marks tests as performance tests