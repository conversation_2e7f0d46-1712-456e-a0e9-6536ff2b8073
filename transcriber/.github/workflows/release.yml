name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio pytest-mock
    
    - name: Set up test environment
      run: |
        echo "GEMINI_API_KEY_1=test_key_1" >> $GITHUB_ENV
        echo "GEMINI_API_KEY_2=test_key_2" >> $GITHUB_ENV
        echo "PODCAST_TEST_MODE=true" >> $GITHUB_ENV
    
    - name: Run full test suite
      run: |
        pytest --cov=src --cov-report=term-missing
    
    - name: Ensure no test artifacts
      run: |
        if [ -d ".pytest_cache" ]; then
          echo "Error: Test artifacts found"
          exit 1
        fi

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine setuptools wheel
    
    - name: Build package
      run: |
        python -m build
        
        # Verify package contents
        twine check dist/*
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: python-package
        path: dist/

  create-release:
    needs: [test, build]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Get version
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
        else
          echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        fi
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: python-package
        path: dist/
    
    - name: Generate changelog
      id: changelog
      run: |
        # Generate changelog from commits since last tag
        if git describe --tags --abbrev=0 2>/dev/null; then
          LAST_TAG=$(git describe --tags --abbrev=0)
          echo "## Changes since $LAST_TAG" > CHANGELOG.md
          git log --pretty=format:"- %s" $LAST_TAG..HEAD >> CHANGELOG.md
        else
          echo "## Initial Release" > CHANGELOG.md
          echo "First release of the Podcast Transcriber" >> CHANGELOG.md
        fi
        
        # Add release notes
        echo "" >> CHANGELOG.md
        echo "## Features" >> CHANGELOG.md
        echo "- Podcast RSS feed processing" >> CHANGELOG.md
        echo "- Speaker diarization and identification" >> CHANGELOG.md
        echo "- WebVTT format output" >> CHANGELOG.md
        echo "- Progress tracking and resume capability" >> CHANGELOG.md
        echo "- API key rotation for quota management" >> CHANGELOG.md
    
    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.version.outputs.version }}
        release_name: Release ${{ steps.version.outputs.version }}
        body_path: CHANGELOG.md
        draft: false
        prerelease: false
    
    - name: Upload Release Assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: dist/
        asset_name: podcast-transcriber-${{ steps.version.outputs.version }}
        asset_content_type: application/zip

  docker:
    needs: [test, build]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: sblumenf/podcast-transcriber
        tags: |
          type=ref,event=tag
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  notify:
    needs: [create-release]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.create-release.result == 'success'
      run: |
        echo "🎉 Release ${{ github.ref_name }} created successfully!"
        echo "Check the release page for download links and changelog."
    
    - name: Notify on failure
      if: needs.create-release.result == 'failure'
      run: |
        echo "❌ Release failed for ${{ github.ref_name }}"
        echo "Check the workflow logs for details."