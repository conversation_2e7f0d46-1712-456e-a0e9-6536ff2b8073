version: '3.8'

services:
  # Main application service
  app:
    build:
      context: .
      target: production
    environment:
      - GEMINI_API_KEY_1=${GEMINI_API_KEY_1:-}
      - GEMINI_API_KEY_2=${GEMINI_API_KEY_2:-}
      - PODCAST_OUTPUT_DIR=/app/data/transcripts
      - PODCAST_LOG_DIR=/app/logs
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    ports:
      - "8000:8000"
    restart: unless-stopped
    
  # Development service with test capabilities
  dev:
    build:
      context: .
      target: development
    environment:
      - GEMINI_API_KEY_1=test_key_1
      - GEMINI_API_KEY_2=test_key_2
      - PODCAST_TEST_MODE=true
      - PODCAST_MOCK_API_CALLS=true
      - PODCAST_DEBUG_MODE=true
    volumes:
      - .:/app:cached
      - ~/.cache/pip:/home/<USER>/.cache/pip
    working_dir: /app
    command: pytest -v
    profiles:
      - dev
    
  # Test runner service
  test:
    build:
      context: .
      target: development
    environment:
      - GEMINI_API_KEY_1=test_key_1
      - GEMINI_API_KEY_2=test_key_2
      - PODCAST_TEST_MODE=true
      - PODCAST_MOCK_API_CALLS=true
    volumes:
      - .:/app:cached
    working_dir: /app
    command: pytest --cov=src --cov-report=term-missing
    profiles:
      - test
      
  # Code quality service
  quality:
    build:
      context: .
      target: development
    volumes:
      - .:/app:cached
    working_dir: /app
    command: >
      sh -c "
        echo 'Running code quality checks...' &&
        black --check src tests &&
        isort --check-only src tests &&
        flake8 src tests --max-line-length=100 --ignore=E203,W503 &&
        echo 'All quality checks passed!'
      "
    profiles:
      - quality

# Named volumes for persistent data
volumes:
  transcripts_data:
  logs_data:
  pip_cache:

# Networks
networks:
  default:
    driver: bridge