[pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test markers
markers =
    unit: Unit tests (no external dependencies)
    integration: Integration tests (may use external services)
    e2e: End-to-end tests (full system tests)
    network: Tests requiring network access
    slow: Slow-running tests
    performance: Performance benchmarking tests
    memory_intensive: Tests that use significant memory

# Coverage settings
addopts = 
    # Memory-efficient test execution
    --maxfail=3
    --tb=short
    --strict-markers
    -p no:cacheprovider
    # Coverage configuration
    --cov=src
    --cov-report=term-missing:skip-covered
    --cov-report=html
    --cov-report=xml
    --cov-fail-under=85
    # Memory optimization: run tests in smaller groups
    --durations=10
    -v

# Coverage configuration
[coverage:run]
source = src
omit = 
    */tests/*
    */venv/*
    */__pycache__/*
    */site-packages/*

[coverage:report]
precision = 2
show_missing = True
skip_covered = False
fail_under = 85

# Exclude patterns for coverage
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml

# Memory-efficient test configuration
[pytest:ini]
# Limit memory usage by running garbage collection between modules
python_classes = Test*
python_functions = test_*

# Timeout configuration to prevent hanging tests
timeout = 300
timeout_method = thread

# Asyncio configuration
asyncio_mode = auto

# Memory optimization settings
[tool:pytest]
# Run garbage collection after each test
addopts = --forked

# Test collection optimization
collect_ignore = ["setup.py"]
collect_ignore_glob = ["**/node_modules/**", "**/venv/**"]