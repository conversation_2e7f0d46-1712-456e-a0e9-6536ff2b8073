#!/usr/bin/env bash
#
# Podcast Transcriber Wrap<PERSON>
# This script handles Python detection and virtual environment activation
#

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Check if we're in a virtual environment
if [[ -z "${VIRTUAL_ENV}" ]]; then
    # Check if venv exists in the script directory
    if [[ -d "${SCRIPT_DIR}/venv" ]]; then
        echo "Activating virtual environment..."
        source "${SCRIPT_DIR}/venv/bin/activate"
    else
        echo "Warning: No virtual environment found. Dependencies may be missing."
        echo "To create a virtual environment, run:"
        echo "  python3 -m venv ${SCRIPT_DIR}/venv"
        echo "  source ${SCRIPT_DIR}/venv/bin/activate"
        echo "  pip install -r ${SCRIPT_DIR}/requirements.txt"
        echo ""
    fi
fi

# Detect Python command
if command -v python &> /dev/null; then
    PYTHON_CMD="python"
elif command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    echo "Error: Python not found. Please install Python 3.8 or later."
    exit 1
fi

# Run the CLI
exec "${PYTHON_CMD}" -m src.cli "$@"